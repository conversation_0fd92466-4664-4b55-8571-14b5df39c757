import path from "node:path";
// largely adapted from eslint's plugin system
const NAMESPACE_REGEX = /^@.*\//u;
// In eslint this is a parameter - we don't need to support the extra options
const prefix = "commitlint-plugin";
// Replace Windows with posix style paths
function convertPathToPosix(filepath) {
    const normalizedFilepath = path.normalize(filepath);
    const posixFilepath = normalizedFilepath.replace(/\\/gu, "/");
    return posixFilepath;
}
/**
 * Brings package name to correct format based on prefix
 * @param {string} name The name of the package.
 * @returns {string} Normalized name of the package
 * @private
 */
export function normalizePackageName(name) {
    let normalizedName = name;
    /**
     * On Windows, name can come in with Windows slashes instead of Unix slashes.
     * Normalize to Unix first to avoid errors later on.
     * https://github.com/eslint/eslint/issues/5644
     */
    if (normalizedName.indexOf("\\") > -1) {
        normalizedName = convertPathToPosix(normalizedName);
    }
    if (normalizedName.charAt(0) === "@") {
        /**
         * it's a scoped package
         * package name is the prefix, or just a username
         */
        const scopedPackageShortcutRegex = new RegExp(`^(@[^/]+)(?:/(?:${prefix})?)?$`, "u"), scopedPackageNameRegex = new RegExp(`^${prefix}(?:-|$)`, "u");
        if (scopedPackageShortcutRegex.test(normalizedName)) {
            normalizedName = normalizedName.replace(scopedPackageShortcutRegex, `$1/${prefix}`);
        }
        else if (!scopedPackageNameRegex.test(normalizedName.split("/")[1])) {
            /**
             * for scoped packages, insert the prefix after the first / unless
             * the path is already @scope/eslint or @scope/eslint-xxx-yyy
             */
            normalizedName = normalizedName.replace(/^@([^/]+)\/(.*)$/u, `@$1/${prefix}-$2`);
        }
    }
    else if (normalizedName.indexOf(`${prefix}-`) !== 0) {
        normalizedName = `${prefix}-${normalizedName}`;
    }
    return normalizedName;
}
/**
 * Removes the prefix from a fullname.
 * @param {string} fullname The term which may have the prefix.
 * @returns {string} The term without prefix.
 */
export function getShorthandName(fullname) {
    if (fullname[0] === "@") {
        let matchResult = new RegExp(`^(@[^/]+)/${prefix}$`, "u").exec(fullname);
        if (matchResult) {
            return matchResult[1];
        }
        matchResult = new RegExp(`^(@[^/]+)/${prefix}-(.+)$`, "u").exec(fullname);
        if (matchResult) {
            return `${matchResult[1]}/${matchResult[2]}`;
        }
    }
    else if (fullname.startsWith(`${prefix}-`)) {
        return fullname.slice(prefix.length + 1);
    }
    return fullname;
}
/**
 * Gets the scope (namespace) of a term.
 * @param {string} term The term which may have the namespace.
 * @returns {string} The namepace of the term if it has one.
 */
export function getNamespaceFromTerm(term) {
    const match = NAMESPACE_REGEX.exec(term);
    return match ? match[0] : "";
}
//# sourceMappingURL=plugin-naming.js.map