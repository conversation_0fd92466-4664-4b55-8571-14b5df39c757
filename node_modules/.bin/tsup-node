#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/tsup@8.5.0_@swc+core@1.11.29_jiti@2.4.2_tsx@4.19.4_typescript@5.8.3_yaml@2.8.0/node_modules/tsup/dist/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/tsup@8.5.0_@swc+core@1.11.29_jiti@2.4.2_tsx@4.19.4_typescript@5.8.3_yaml@2.8.0/node_modules/tsup/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/tsup@8.5.0_@swc+core@1.11.29_jiti@2.4.2_tsx@4.19.4_typescript@5.8.3_yaml@2.8.0/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/tsup@8.5.0_@swc+core@1.11.29_jiti@2.4.2_tsx@4.19.4_typescript@5.8.3_yaml@2.8.0/node_modules/tsup/dist/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/tsup@8.5.0_@swc+core@1.11.29_jiti@2.4.2_tsx@4.19.4_typescript@5.8.3_yaml@2.8.0/node_modules/tsup/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/tsup@8.5.0_@swc+core@1.11.29_jiti@2.4.2_tsx@4.19.4_typescript@5.8.3_yaml@2.8.0/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../tsup/dist/cli-node.js" "$@"
else
  exec node  "$basedir/../tsup/dist/cli-node.js" "$@"
fi
