#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/JSONStream@1.3.5_fb8f73c374dcf7070ffed90b688edabd/node_modules/JSONStream/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/JSONStream@1.3.5_fb8f73c374dcf7070ffed90b688edabd/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/JSONStream@1.3.5_fb8f73c374dcf7070ffed90b688edabd/node_modules/JSONStream/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/JSONStream@1.3.5_fb8f73c374dcf7070ffed90b688edabd/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../JSONStream/bin.js" "$@"
else
  exec node  "$basedir/../JSONStream/bin.js" "$@"
fi
