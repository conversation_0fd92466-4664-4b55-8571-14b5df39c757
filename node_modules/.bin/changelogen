#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/changelogen@0.6.1/node_modules/changelogen/dist/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/changelogen@0.6.1/node_modules/changelogen/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/changelogen@0.6.1/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/changelogen@0.6.1/node_modules/changelogen/dist/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/changelogen@0.6.1/node_modules/changelogen/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/changelogen@0.6.1/node_modules:/Users/<USER>/Projects/new-eslint-config/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../changelogen/dist/cli.mjs" "$@"
else
  exec node  "$basedir/../changelogen/dist/cli.mjs" "$@"
fi
